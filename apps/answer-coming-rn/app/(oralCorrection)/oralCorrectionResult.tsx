import {
  YTI<PERSON>,
  YTS<PERSON><PERSON>View,
  YTT<PERSON>t,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { useSafeAreaInsets, useImageSize } from '@jgl/biz-func';
import { StateView } from '@jgl/biz-components';
import { container } from '@jgl/container';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useState, useMemo, useRef } from 'react';
import { queryUserCorrectDetailByRecordId } from '../../api/api';
import {
  OralCorrectionBottomSheet,
  OralCorrectionBottomSheetRef,
} from '../../components/oralCorrection/OralCorrectionBottomSheet';

interface AnswerItem {
  /** 识别结果 */
  recValue: string;
  /** 正确答案 */
  value: string;
}

interface ResultInfo {
  coord: number[];
  judgeResult: number;
  answer?: AnswerItem[];
}

interface CheckDTO {
  recordId?: string;
  imageUrl?: string;
  originImageUrl?: string;
  resultInfos?: Array<{
    recResult?: ResultInfo[];
  }>;
  rightNums?: number;
  errorNums?: number;
  noReplyNums?: number;
  errorMsg?: string;
}

/**
 * 口算批改结果页面 - 独立页面，用于查看历史批改记录
 * 完全参照小程序版本的逻辑
 */
const OralCorrectionResultScreen = () => {
  const { bottom } = useSafeAreaInsets();
  const { recordId } = useLocalSearchParams<{ recordId: string }>();
  const [checkDTO, setCheckDTO] = useState<CheckDTO | undefined>();
  const [loading, setLoading] = useState(false);
  const [answerItems, setAnswerItems] = useState<AnswerItem[]>([]);
  const bottomSheetRef = useRef<OralCorrectionBottomSheetRef>(null);

  // 使用useImageSize hook来处理图片尺寸，参照小程序版本
  const { size, init } = useImageSize({ imageUrl: checkDTO?.originImageUrl });

  const [intervalTime, setIntervalTime] = useState<number | undefined>();

  const { imageUrl } = checkDTO ?? {};

  // 获取批改结果 - 完全参照小程序版本的逻辑
  const getResult = useCallback(async () => {
    if (!recordId) {
      return;
    }
    const { data } = await container.net().fetch(
      queryUserCorrectDetailByRecordId({
        recordId: String(recordId),
      }),
    );

    // 未批改成功清除定时器
    if (data?.errorCode) {
      setIntervalTime(undefined);
      setLoading(false);
      setCheckDTO(data);

      // 批改成功清除定时器
    } else if (data?.resultInfos?.length) {
      setIntervalTime(undefined);
      setLoading(false);
      setCheckDTO(data);
    }
  }, [recordId]);

  useEffect(() => {
    if (intervalTime) {
      const interval = setInterval(getResult, intervalTime);
      return () => clearInterval(interval);
    }
  }, [getResult, intervalTime]);

  useEffect(() => {
    if (recordId) {
      setLoading(true);
      setIntervalTime(1000);
    }
  }, [recordId]);

  useEffect(() => {
    init();
  }, [init]);

  // 渲染题目作答状态 - 参照小程序版本
  const renderExamStatus = useCallback((answer: ResultInfo) => {
    const isRight = [1, 2].includes(answer.judgeResult); // 正确、半对
    const isError = [0].includes(answer.judgeResult); // 错误
    const notAnswer = [3, 4].includes(answer.judgeResult); // 未作答、问号

    let statusColor = '#ccc';
    let statusText = '?';
    
    if (isRight) {
      statusColor = '#52C41A';
      statusText = '✓';
    } else if (isError) {
      statusColor = '#FF4D4F';
      statusText = '✗';
    }

    return (
      <YTTouchable
        w={28}
        h={28}
        borderRadius={14}
        bg={statusColor}
        ai='center'
        jc='center'
        onPress={() => {
          if (isError && answer.answer) {
            setAnswerItems(answer.answer || []);
            bottomSheetRef.current?.present();
          }
        }}
      >
        <YTText color='white' fontSize={16} fontWeight='bold'>
          {statusText}
        </YTText>
      </YTTouchable>
    );
  }, []);

  // 渲染作答框 - 参照小程序版本
  const renderRect = useCallback(
    (list?: ResultInfo[]) => {
      return list?.map((answer, index) => {
        const [x1 = 0, y1 = 0, x2 = 0, y2 = 0] = answer?.coord || [];

        return (
          <YTView
            key={`${answer?.coord.join(',')}-${index}`}
            position='absolute'
            w={size.width * (x2 - x1)}
            h={size.height * (y2 - y1)}
            left={size.width * x1}
            top={size.height * y1}
            ai='center'
            jc='center'
            borderRadius={8}
            borderWidth={1}
            borderColor='white'
          >
            {renderExamStatus(answer)}
          </YTView>
        );
      });
    },
    [renderExamStatus, size.height, size.width],
  );

  // 渲染图片 - 参照小程序版本
  const renderImage = useMemo(
    () =>
      imageUrl ? (
        <YTImage
          source={{ uri: imageUrl }}
          style={{ width: size.width, height: size.height }}
          contentFit='contain'
        />
      ) : null,
    [imageUrl, size],
  );

  // 计算正确率 - 参照小程序版本
  const rightPercent = useMemo(() => {
    const total =
      (checkDTO?.rightNums ?? 0) +
      (checkDTO?.errorNums ?? 0) +
      (checkDTO?.noReplyNums ?? 0);
    return total > 0 ? ((checkDTO?.rightNums ?? 0) / total) * 100 : 0;
  }, [checkDTO?.rightNums, checkDTO?.errorNums, checkDTO?.noReplyNums]);

  // 渲染批改结果描述 - 参照小程序版本
  const renderResultDescription = useMemo(
    () =>
      checkDTO?.resultInfos?.length ? (
        <YTYStack ai='center' mb={8}>
          <YTXStack ai='center' gap={4}>
            <YTText fontSize={18} fontWeight='600' color='#1f1f1f'>
              正确
            </YTText>
            <YTText fontSize={18} fontWeight='600' color='#52C41A'>
              {checkDTO?.rightNums}
            </YTText>
            <YTText fontSize={18} fontWeight='600' color='#1f1f1f'>
              题，错误
            </YTText>
            <YTText fontSize={18} fontWeight='600' color='#FF4D4F'>
              {checkDTO?.errorNums}
            </YTText>
            <YTText fontSize={18} fontWeight='600' color='#1f1f1f'>
              题，正确率
            </YTText>
            <YTText fontSize={18} fontWeight='600' color='#52C41A'>
              {rightPercent.toFixed(2)}%
            </YTText>
          </YTXStack>
        </YTYStack>
      ) : null,
    [
      checkDTO?.errorNums,
      checkDTO?.resultInfos?.length,
      checkDTO?.rightNums,
      rightPercent,
    ],
  );

  const handleToRecord = useCallback(() => {
    router.push('/(oralCorrection)/oralCorrectionRecord');
  }, []);

  const onClickTakeAgain = useCallback(() => {
    router.back();
  }, []);

  return (
    <YTYStack flex={1} bg='white' style={{ paddingBottom: bottom || 16 }}>
      <Stack.Screen
        options={{
          title: '批改结果',
          headerBackTitle: '返回',
        }}
      />

      <StateView
        ytClassName='flex-1 w-full overflow-hidden'
        isLoading={loading}
        isEmpty={checkDTO?.resultInfos?.length === 0}
        error={checkDTO?.errorMsg ? new Error(checkDTO?.errorMsg) : null}
        emptyProps={{
          message: '暂未识别出题目，再拍一次吧~',
        }}
        errorProps={{
          message: checkDTO?.errorMsg,
        }}
      >
        <YTYStack flex={1}>
          <YTScrollView
            flex={1}
            showsVerticalScrollIndicator={false}
          >
            <YTYStack flex={1}>
              <YTView flex={1} position='relative'>
                {renderImage}
                
                {checkDTO?.resultInfos?.map((result, index) => {
                  return renderRect(result?.recResult);
                })}
              </YTView>

              <YTYStack h={86} ai='center' jc='center' px={16}>
                {renderResultDescription}
                <YTXStack ai='center' gap={4}>
                  <YTText fontSize={14} color='#4E5969'>
                    点击
                  </YTText>
                  <YTView
                    w={28}
                    h={22}
                    ai='center'
                    jc='center'
                    bg='#FF4D4F'
                    borderRadius={14}
                  >
                    <YTText color='white' fontSize={12} fontWeight='bold'>
                      ✗
                    </YTText>
                  </YTView>
                  <YTText fontSize={14} color='#4E5969'>
                    标记的错误答案题，可查看正确答案
                  </YTText>
                </YTXStack>
              </YTYStack>
            </YTYStack>

            {/* 底部按钮区域 - 参照小程序版本 */}
            <YTXStack px={16} py={12} gap={12} ai='center'>
              <YTTouchable ai='center' onPress={handleToRecord}>
                <YTYStack ai='center' gap={4}>
                  <YTText fontSize={24}>📋</YTText>
                  <YTText fontSize={12} color='#60646C'>
                    批改记录
                  </YTText>
                </YTYStack>
              </YTTouchable>

              <YTTouchable
                flex={1}
                h={44}
                bg='#165DFF'
                borderRadius={22}
                ai='center'
                jc='center'
                onPress={onClickTakeAgain}
              >
                <YTXStack ai='center' gap={8}>
                  <YTText fontSize={20}>📷</YTText>
                  <YTText fontSize={16} fontWeight='500' color='white'>
                    再拍一张
                  </YTText>
                </YTXStack>
              </YTTouchable>
            </YTXStack>
          </YTScrollView>
        </YTYStack>
      </StateView>

      {/* 底部弹窗 - 显示错误答案详情 */}
      <OralCorrectionBottomSheet
        ref={bottomSheetRef}
        answerItems={answerItems}
      />
    </YTYStack>
  );
};

export default OralCorrectionResultScreen;
