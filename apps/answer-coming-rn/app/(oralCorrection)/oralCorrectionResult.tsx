import { YTYStack } from '@bookln/cross-platform-components';
import { useSafeAreaInsets } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { queryUserCorrectDetailByRecordId } from '../../api/api';
import { OralCorrectionResult } from '../../components/oralCorrection/OralCorrectionResult';
import { OralCorrectionResult as ResultType } from '../../hooks/useOralCorrection';

/**
 * 口算批改结果页面 - 独立页面，用于查看历史批改记录
 */
const OralCorrectionResultScreen = () => {
  const { bottom } = useSafeAreaInsets();
  const { recordId } = useLocalSearchParams<{ recordId: string }>();
  const [checkDTO, setCheckDTO] = useState<ResultType | undefined>();
  const [loading, setLoading] = useState(false);
  const [intervalTime, setIntervalTime] = useState<number | undefined>();

  // 获取批改结果 - 完全参照小程序版本的逻辑
  const getResult = useCallback(async () => {
    if (!recordId) {
      return;
    }
    const { data } = await container.net().fetch(
      queryUserCorrectDetailByRecordId({
        recordId: String(recordId),
      }),
    );

    // 未批改成功清除定时器
    if (data?.errorCode) {
      setIntervalTime(undefined);
      setLoading(false);
      setCheckDTO(data);

      // 批改成功清除定时器
    } else if (data?.resultInfos?.length) {
      setIntervalTime(undefined);
      setLoading(false);
      setCheckDTO(data);
    }
  }, [recordId]);

  useEffect(() => {
    if (intervalTime) {
      const interval = setInterval(getResult, intervalTime);
      return () => clearInterval(interval);
    }
  }, [getResult, intervalTime]);

  useEffect(() => {
    if (recordId) {
      setLoading(true);
      setIntervalTime(1000);
    }
  }, [recordId]);

  // 处理按钮回调
  const handleViewHistory = useCallback(() => {
    router.push('/(oralCorrection)/oralCorrectionRecord');
  }, []);

  const handleRetake = useCallback(() => {
    router.back();
  }, []);

  return (
    <YTYStack flex={1} bg='white' style={{ paddingBottom: bottom || 16 }}>
      <Stack.Screen
        options={{
          title: '批改结果',
          headerBackTitle: '返回',
        }}
      />

      <OralCorrectionResult
        visible={true}
        imageUrl={checkDTO?.imageUrl}
        checkDTO={checkDTO}
        loading={loading}
        onRetake={handleRetake}
        onViewHistory={handleViewHistory}
      />
    </YTYStack>
  );
};

export default OralCorrectionResultScreen;
