import {
  Y<PERSON><PERSON>,
  YTSc<PERSON>View,
  YTStateView,
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { container } from '@jgl/container';
import { AcIcon } from '@jgl/icon/src';
import { router, showToast, useDidShow, useRouterParams } from '@jgl/utils';
import { useMount } from 'ahooks';
import { Stack, router as expoRouter } from 'expo-router';
import { useAtom } from 'jotai';
import { isEmpty } from 'lodash';
import { type Key, useCallback, useMemo, useRef, useState } from 'react';
import {
  type NativeSyntheticEvent,
  Platform,
  Pressable,
  StyleSheet,
  type TextInputSubmitEditingEventData,
} from 'react-native';
import { listByIsbnOrName } from '../api/api';
import type {
  BookDTO,
  ChooseOptionDTO,
  FetchBookParams,
  FilterParam,
} from '../api/dto';
import { gradesAtom, subjectsAtom, versionsAtom } from '../atom';
import { BookItem } from '../components/BookItem';
import { EmptySearchResult } from '../components/EmptySearchResult';
import {
  FilterGuideModal,
  type FilterGuideModalRef,
} from '../components/FilterGuideModal';
import { LoadMoreText } from '../components/LoadMoreText';
import { Search } from '../components/Search';
import { Select } from '../components/Select';
import { barCodeReg, shouldShowFilterGuideKey } from '../constants';
import { useCameraAuth } from '../hooks/useCameraAuth';
import { useFeatureFlags } from '../hooks/useFeatureFlags';
import { getShouldShowGuide } from '../utils';
import { getSubjectTags, getTags, getVersionTags } from '../utils/getTags';
import { routerMap } from '../utils/routerMap';
import {
  useNavigationBarBarHeight,
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';

/** 搜索结果页 */
const ResultScreen = () => {
  const { search = '', from = 'search' } = useRouterParams();

  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = useNavigationBarHeight();
  const barHeight = useNavigationBarBarHeight();

  const { isCheating } = useFeatureFlags();
  const [resultList, setResultList] = useState<BookDTO[]>([]);
  const [loading, setLoading] = useState(false);

  const [loadingMore, setLoadingMore] = useState(false);
  const [searchContent, setSearchContent] = useState(search);
  const [filterParam, setFilterParam] = useState<FilterParam>({});
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalRecords: 0,
  });

  const filterGuideModalRef = useRef<FilterGuideModalRef>(null);

  const [grades, setGrades] = useAtom(gradesAtom);
  const [subjects, setSubjects] = useAtom(subjectsAtom);
  const [versions, setVersions] = useAtom(versionsAtom);
  const [isShowStrategy, setIsShowStrategy] = useState(true);

  const onClickClose = useCallback(() => {
    setIsShowStrategy(false);
  }, []);

  const fetchTags = useCallback(
    async (pCode: string, cb: (tags: ChooseOptionDTO[]) => void) => {
      const tags = await getTags(pCode, true);
      cb(tags);
    },
    [],
  );

  /** 查询结果 */
  const fetchResults = useCallback(
    async (
      param?: FetchBookParams,
      isLoadMore?: boolean,
      activeSearch?: string,
    ) => {
      const realSearch = activeSearch ?? searchContent ?? search;
      if (realSearch) {
        if (isLoadMore) {
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        const res = await container
          .net()
          .fetch(
            listByIsbnOrName({
              isbnOrName: realSearch,
              ...param,
              pageSize: param?.pageSize ?? 20,
              currentPage: param?.currentPage ?? 1,
              photoSearch: from === 'scan' ? true : false,
            }),
          )
          .finally(() => {
            if (isLoadMore) {
              setLoadingMore(false);
            } else {
              setLoading(false);
            }
          });

        const { success, data } = res;

        if (success && data) {
          const {
            pageData = [],
            currentPage = 1,
            totalPages = 0,
            totalRecords = 0,
          } = data;
          if (isLoadMore) {
            setResultList([...resultList, ...pageData]);
          } else {
            setResultList(pageData);
          }
          setPagination({
            currentPage,
            totalPages,
            totalRecords,
          });
        }
      }
    },
    [from, resultList, search, searchContent],
  );

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event;
    if (loadingMore) {
      return;
    }

    if (layoutMeasurement.height + contentOffset.y >= contentSize.height - 20) {
      const { currentPage = 1, totalPages = 0 } = pagination;
      if (currentPage < totalPages) {
        fetchResults({ ...filterParam, currentPage: currentPage + 1 }, true);
      }
    }
  };

  const handleShowFilterGuideModal = useCallback(async () => {
    const show = await getShouldShowGuide({ key: shouldShowFilterGuideKey });
    if (show) {
      filterGuideModalRef.current?.showModal();
    }
  }, []);

  useMount(() => {
    fetchResults();

    fetchTags('grade_20240805', setGrades);
    getSubjectTags(setSubjects);
    getVersionTags(setVersions);

    handleShowFilterGuideModal();
  });

  const onChangeFilter = useCallback(
    (value: Key, key: keyof FilterParam) => {
      setFilterParam((pre) => {
        const result = {
          ...pre,
          [key]: value,
        };

        fetchResults({ ...result, currentPage: 1 });
        return result;
      });
    },
    [fetchResults],
  );

  const onClickAnswerBook = useCallback(
    (id?: number) => {
      if (isCheating) {
        showToast({ title: '已加入书架' });
      } else {
        expoRouter.push(`/book?id=${id}`);
      }
    },
    [isCheating],
  );

  const barTitle = useMemo(() => {
    if (barCodeReg.test(search)) {
      return `条形码 ${search}`;
    } else {
      return search;
    }
  }, [search]);

  // TODO: 空态图片替换

  const onClickStrategy = useCallback(() => {
    router.push(`${routerMap.searchStrategy}`);
  }, []);

  /** 点击扫码 */
  const handleClickScan = useCallback(() => {
    router.push(`${routerMap.barcodeScan}`);
  }, []);

  const { onClickOpenCamera: onClickScan } = useCameraAuth({
    onOk: handleClickScan,
  });

  const onSubmitEditing = useCallback(
    (e: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => {
      const text = e.nativeEvent.text;
      setSearchContent(text);

      if (!isEmpty(text)) {
        fetchResults(undefined, undefined, text);
      }
    },
    [fetchResults],
  );

  /**
   *
   */

  const onPressNavBack = useCallback(() => {
    router.back();
  }, []);

  const navBarHeight = safeInsets.top + navigationBarHeight;

  const renderNavBar = useMemo(() => {
    return (
      <YTXStack
        position='absolute'
        top={0}
        left={0}
        right={0}
        zIndex={1000}
        w='$full'
        h={navBarHeight}
        ai='center'
        bg='transparent'
      >
        <YTXStack
          position='absolute'
          w='$full'
          left={0}
          right={0}
          bottom={0}
          h={Platform.OS === 'ios' ? barHeight : undefined}
          py={10}
          px={16}
          jc='space-between'
          ai='center'
          bg='transparent'
        >
          <YTTouchable
            ai='center'
            jc='center'
            onPress={onPressNavBack}
            style={{
              width: 32,
              height: 32,
            }}
          >
            <YTImage
              source={require('../assets/images/ic_back.png')}
              w={24}
              h={24}
            />
          </YTTouchable>
          <YTXStack flexDirection='row' alignItems='center' flex={1}>
            <Search
              placeholder={`输入书名 / 条形码找${isCheating ? '书' : '答案'}`}
              backgroundColor={'#F8F9FB'}
              defaultValue={barTitle}
              containerStyle={styles.headerSearchContainer}
              clearButtonMode={'while-editing'}
              onSubmitEditing={onSubmitEditing}
              renderRightIcon={
                <Pressable style={styles.searchRightIcon} onPress={onClickScan}>
                  <YTImage source={AcIcon.IcScanBlack} height={24} width={24} />
                </Pressable>
              }
            />
          </YTXStack>
        </YTXStack>
      </YTXStack>
    );
  }, [
    barHeight,
    barTitle,
    isCheating,
    navBarHeight,
    onClickScan,
    onPressNavBack,
    onSubmitEditing,
  ]);

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      {renderNavBar}

      <YTYStack
        position='relative'
        width='100%'
        height='100%'
        flexDirection='column'
        overflow='visible'
        paddingTop={navigationBarHeight + safeInsets.top}
      >
        <YTXStack
          width='100%'
          flexDirection='row'
          alignItems='center'
          justifyContent='space-around'
          borderBottomWidth={1}
          borderColor='#F0F0F0'
          backgroundColor='white'
          paddingLeft={16}
          paddingRight={20}
          zIndex={1000}
        >
          <Select
            key='grade'
            title='全部年级'
            options={grades}
            value={filterParam.gradeCode}
            onChange={(val) => onChangeFilter(val, 'gradeCode')}
          />
          <YTXStack height={16} width={1} backgroundColor='#F0F0F0' />
          <Select
            key='subject'
            title='全部学科'
            options={subjects}
            value={filterParam.subjectCode}
            onChange={(val) => onChangeFilter(val, 'subjectCode')}
          />
          <YTXStack height={16} width={1} backgroundColor='#F0F0F0' />
          <Select
            key='version'
            title='全部版本'
            options={versions}
            value={filterParam.versionCode}
            onChange={(val) => onChangeFilter(val, 'versionCode')}
          />
        </YTXStack>

        <YTYStack
          flex={1}
          width='100%'
          flexDirection='column'
          overflow='hidden'
          backgroundColor='white'
        >
          <YTXStack flex={1} width='100%' flexDirection='column'>
            {pagination.totalRecords > 0 ? (
              <YTXStack
                height={44}
                width='100%'
                flexShrink={0}
                flexDirection='row'
                alignItems='center'
                paddingHorizontal={16}
              >
                <YTText fontSize={16} fontWeight='bold'>
                  共 {pagination.totalRecords} 个结果
                </YTText>
              </YTXStack>
            ) : null}
            <YTXStack style={styles.stateViewWrap}>
              <YTStateView
                isLoading={loading}
                isEmpty={pagination.totalRecords === 0}
                emptyProps={{
                  render: () => {
                    return <EmptySearchResult description={'暂无数据'} />;
                  },
                }}
              >
                <YTYStack
                  flex={1}
                  width='100%'
                  flexDirection='column'
                  overflow='hidden'
                >
                  <YTScrollView
                    width='100%'
                    flex={1}
                    paddingHorizontal={16}
                    scrollEventThrottle={48}
                    onScroll={({ nativeEvent }) => handleScroll(nativeEvent)}
                  >
                    {resultList.map((item) => {
                      return (
                        <BookItem
                          key={item.id}
                          item={item}
                          onClick={onClickAnswerBook}
                          highLightName={searchContent}
                        />
                      );
                    })}

                    <LoadMoreText
                      hasMore={pagination.currentPage < pagination.totalPages}
                    />
                  </YTScrollView>
                </YTYStack>
              </YTStateView>
            </YTXStack>

            {isShowStrategy && (
              <YTXStack style={styles.strategyContainer}>
                <Pressable onPress={onClickClose} style={styles.closeBtn}>
                  <YTImage source={AcIcon.IcCloseGrey} height={24} width={24} />
                </Pressable>
                <Pressable
                  onPress={onClickStrategy}
                  style={styles.strategyPressable}
                >
                  <YTImage
                    source={require('../assets/images/ic_strategy2.png')}
                    height={52}
                    width={52}
                  />
                  <YTImage
                    source={require('../assets/images/ic_strategy1.png')}
                    height={24}
                    width={62}
                    top={3}
                  />
                  <YTImage
                    source={require('../assets/images/ic_strategy3.png')}
                    height={27}
                    width={24}
                    position='absolute'
                    right={0}
                    top={20}
                  />
                </Pressable>
              </YTXStack>
            )}
          </YTXStack>
        </YTYStack>
      </YTYStack>

      <FilterGuideModal ref={filterGuideModalRef} />
    </>
  );
};

const styles = StyleSheet.create({
  searchRightIcon: {
    marginRight: 2,
    alignItems: 'center',
  },
  headerSearchContainer: {
    paddingVertical: 0,
  },
  stateViewWrap: {
    flex: 1,
    width: '100%',
    overflow: 'hidden',
    flexDirection: 'column',
  },
  strategyContainer: {
    position: 'absolute',
    right: 3,
    bottom: '10%',
    backgroundColor: 'transparent',
  },
  closeBtn: {
    alignSelf: 'flex-end',
  },
  strategyPressable: {
    position: 'relative',
    alignItems: 'center',
  },
});

export default ResultScreen;
