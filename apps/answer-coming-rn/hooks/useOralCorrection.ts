import { useCallback, useRef, useState } from 'react';
import { container } from '@jgl/container';
import { OSSUploadNative } from '@jgl/upload/OSSUpload.native';
import { showToast } from '@jgl/utils';
import { useUnmount } from 'ahooks';
import { router } from 'expo-router';
import {
  cameraCorrect,
  queryUserCorrectDetailByRecordId,
  get15DayFileUploadInfoFor,
} from '../api/api';

export enum OralCorrectionProgress {
  Camera = 'camera',
  Processing = 'processing',
  Result = 'result',
}

export interface OralCorrectionResult {
  recordId?: string;
  imageUrl?: string;
  originImageUrl?: string;
  resultInfos?: any[];
  rightNums?: number;
  errorNums?: number;
  errorMsg?: string;
}

export const useOralCorrection = () => {
  const [progress, setProgress] = useState<OralCorrectionProgress>(
    OralCorrectionProgress.Camera,
  );
  const [imageUrl, setImageUrl] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [checkDTO, setCheckDTO] = useState<OralCorrectionResult>();

  const recordIdRef = useRef<string>();
  const intervalRef = useRef<NodeJS.Timeout>();
  const timeoutRef = useRef<NodeJS.Timeout>();

  const clearTimers = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  }, []);

  const fetchResult = useCallback(async () => {
    if (!recordIdRef.current) return;

    try {
      const res = await container
        .net()
        .fetch(
          queryUserCorrectDetailByRecordId({ recordId: recordIdRef.current }),
        );

      if (res.success && res.data) {
        const { resultInfos, rightNums, errorNums, originImageUrl, imageUrl } =
          res.data;

        if (resultInfos && resultInfos.length > 0) {
          setCheckDTO({
            recordId: recordIdRef.current,
            imageUrl: imageUrl || originImageUrl, // 优先使用裁剪后的图片
            originImageUrl,
            resultInfos,
            rightNums,
            errorNums,
          });
          setLoading(false);
          clearTimers();
        }
      }
    } catch (error) {
      console.error('获取批改结果失败:', error);
    }
  }, [clearTimers]);

  const uploadImage = useCallback(
    async (uri: string): Promise<string | undefined> => {
      try {
        const uploadResult = await OSSUploadNative.uploadToOSSWith15DaysExpiry({
          tmpPath: uri,
          options: {
            net: container.net(),
          },
          bizCode: 'ac',
        });
        return uploadResult;
      } catch (error) {
        console.error('图片上传失败:', error);
        showToast({ title: '图片上传失败' });
        return undefined;
      }
    },
    [],
  );

  const submitCorrection = useCallback(
    async (imageUri: string) => {
      setLoading(true);
      setProgress(OralCorrectionProgress.Processing);

      try {
        // 上传图片
        const uploadedUrl = await uploadImage(imageUri);
        if (!uploadedUrl) {
          setLoading(false);
          setProgress(OralCorrectionProgress.Camera);
          return;
        }

        setImageUrl(uploadedUrl);

        // 提交批改请求
        const res = await container
          .net()
          .fetch(cameraCorrect({ url: uploadedUrl }));

        if (res.success && res.data?.recordId) {
          recordIdRef.current = res.data.recordId;
          setProgress(OralCorrectionProgress.Result);

          // 开始轮询获取结果
          intervalRef.current = setInterval(fetchResult, 1000);

          // 60秒超时
          timeoutRef.current = setTimeout(() => {
            clearTimers();
            setLoading(false);
            setCheckDTO({
              errorMsg: '批改超时，请重试',
            });
          }, 60000);
        } else {
          setLoading(false);
          setProgress(OralCorrectionProgress.Camera);
          setCheckDTO({
            errorMsg: res.msg || '批改失败，请重试',
          });
        }
      } catch (error) {
        console.error('提交批改失败:', error);
        setLoading(false);
        setProgress(OralCorrectionProgress.Camera);
        setCheckDTO({
          errorMsg: '网络错误，请重试',
        });
      }
    },
    [uploadImage, fetchResult, clearTimers],
  );

  const resetCorrection = useCallback(() => {
    clearTimers();
    setProgress(OralCorrectionProgress.Camera);
    setImageUrl(undefined);
    setCheckDTO(undefined);
    setLoading(false);
    recordIdRef.current = undefined;
  }, [clearTimers]);

  const goBack = useCallback(() => {
    router.back();
  }, []);

  useUnmount(() => {
    clearTimers();
  });

  return {
    progress,
    imageUrl,
    loading,
    checkDTO,
    submitCorrection,
    resetCorrection,
    goBack,
  };
};
