import { LoadMoreText, StateView } from '@jgl/biz-components';
import { PaginationParams } from '@jgl/biz-func';
import { NavigationBar, WithLogin } from '@jgl/components';
import { container } from '@jgl/container';
import { showToast } from '@jgl/utils';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import { useMount } from 'ahooks';
import routerMap from 'config/routerMap';
import { useCallback, useMemo, useState } from 'react';
import { userCorrectHistory } from 'src/api-dto/api';
import { CorrectionRecordDTO } from 'src/api-dto/dto';
import { groupByTimestamp, handleJumpSubPages } from 'src/utils';

/** 口算批改记录 */
const OralCorrectionRecordScreen = () => {
  const [recordList, setRecordList] = useState<CorrectionRecordDTO[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalRecords: 0,
  });

  const fetchData = useCallback(
    async (param?: PaginationParams, isLoadMore?: boolean) => {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }
      const res = await container
        .net()
        .fetch(
          userCorrectHistory({
            pageSize: param?.pageSize ?? 50,
            currentPage: param?.currentPage ?? 1,
          }),
        )
        .finally(() => {
          if (isLoadMore) {
            setLoadingMore(false);
          } else {
            setLoading(false);
          }
        });

      const { success, data, msg } = res;
      if (success && data) {
        const {
          pageData = [],
          currentPage = 1,
          totalPages = 0,
          totalRecords = 0,
        } = data;
        if (isLoadMore) {
          setRecordList((pre) => {
            const newData = [...pre, ...pageData];
            return newData;
          });
        } else {
          setRecordList(pageData);
        }

        setPagination({
          currentPage,
          totalPages,
          totalRecords,
        });
      } else {
        msg && showToast({ title: msg });
      }
    },
    [],
  );

  useMount(() => {
    fetchData();
  });

  const onScrollToBottom = useCallback(() => {
    if (loadingMore) {
      return;
    }
    const { currentPage = 1, totalPages = 0 } = pagination;
    if (currentPage < totalPages) {
      fetchData({ currentPage: currentPage + 1 }, true);
    }
  }, [fetchData, loadingMore, pagination]);

  const handleClickImage = useCallback((recordId?: string) => {
    if (recordId) {
      handleJumpSubPages(
        `${routerMap.oralCorrectionResult}?recordId=${recordId}`,
      );
    }
  }, []);

  const content = useMemo(() => {
    if (recordList) {
      const formatList = groupByTimestamp(recordList);
      const list = Object.entries(formatList);
      return list.map(([key, value]) => (
        <View className='w-full flex-col px-[16PX]' key={key}>
          <View className='flex h-[48PX] items-center'>
            <Text className='text-text font-medium' style={{ fontSize: 16 }}>
              {key}
            </Text>
          </View>
          <View className='flex w-full flex-wrap'>
            {value.map((item, index) => {
              const { sourceUrl = '' } = item;
              return (
                <View
                  key={item.id}
                  className='flex-center mb-[16PX] h-[140PX] max-w-[105PX] overflow-hidden rounded-[6PX] border border-solid border-[#E5E6EB]'
                  style={{
                    width: 'calc(100vw / 3 - 16PX)',
                    marginRight: (index + 1) % 3 === 0 ? 0 : 16,
                  }}
                >
                  <Image
                    onClick={() => handleClickImage(item.recordId)}
                    className='w-full'
                    mode='widthFix'
                    src={sourceUrl}
                  />
                </View>
              );
            })}
          </View>
        </View>
      ));
    }
  }, [handleClickImage, recordList]);

  return (
    <View className='flex h-full w-full flex-col bg-white'>
      <NavigationBar title='批改记录' back type='left' />

      <StateView
        ytClassName='flex-1 w-full overflow-hidden'
        isLoading={loading}
        emptyProps={{
          message: '暂无批改记录',
        }}
      >
        <ScrollView
          enableFlex
          showScrollbar={false}
          scrollY
          className='flex h-full w-full flex-col'
          onScrollToLower={onScrollToBottom}
        >
          {content}
          <LoadMoreText
            hasMore={pagination.currentPage < pagination.totalPages}
          />
        </ScrollView>
      </StateView>
    </View>
  );
};

export default WithLogin(OralCorrectionRecordScreen);
